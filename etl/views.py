from rest_framework.views import APIView
import pandas as pd
from google.cloud import bigquery
from rest_framework import serializers
from rest_framework.response import Response

from picking.models import PickOrder


# Unified schema definitions with both BigQuery and pandas types
ORDER_ITEMS_FIELD_DEFINITIONS = [
    ("id", "INTEGER", "Int64", "NULL<PERSON><PERSON>"),
    ("sku", "STRING", "string", "NULLABLE"),
    ("name", "STRING", "string", "NULLABLE"),
    ("number", "FLOAT", "float64", "NULLABLE"),
    ("status", "STRING", "string", "NULLABLE"),
    ("skutype", "INTEGER", "Int64", "NULLABLE"),
    ("discount", "STRING", "string", "NULLABLE"),
    ("mfg_date", "INTEGER", "Int64", "NULLABLE"),
    ("unittext", "STRING", "string", "NULLABLE"),
    ("productid", "INTEGER", "Int64", "NULLABLE"),
    ("totalprice", "FLOAT", "float64", "NULLABLE"),
    ("producttype", "INTEGER", "Int64", "NULLABLE"),
    ("sku_barcode", "STRING", "string", "NULLABLE"),
    ("serialnolist", "INTEGER", "object", "REPEATED"),  # Special handling for REPEATED
    ("discountamount", "FLOAT", "float64", "NULLABLE"),
    ("eso_vatpercent", "FLOAT", "float64", "NULLABLE"),
    ("original_price", "FLOAT", "float64", "NULLABLE"),
    ("pricepernumber", "FLOAT", "float64", "NULLABLE"),
    (
        "return_quantity",
        "INTEGER",
        "float64",
        "NULLABLE",
    ),  # Using float64 for nullable integers
    ("seller_discount", "FLOAT", "float64", "NULLABLE"),
    ("shipping_amount", "FLOAT", "float64", "NULLABLE"),
    ("platform_discount", "FLOAT", "float64", "NULLABLE"),
    ("original_shipping_amount", "FLOAT", "float64", "NULLABLE"),
    ("seller_shipping_discount", "FLOAT", "float64", "NULLABLE"),
    ("platform_shipping_discount", "FLOAT", "float64", "NULLABLE"),
    ("order_id", "INTEGER", "Int64", "NULLABLE"),
]

ORDERS_FIELD_DEFINITIONS = [
    ("id", "INTEGER", "Int64", "NULLABLE"),
    ("tag", "INTEGER", "object", "REPEATED"),  # Special handling for REPEATED
    ("line", "INTEGER", "Int64", "NULLABLE"),
    ("isCOD", "BOOLEAN", "boolean", "NULLABLE"),
    ("amount", "FLOAT", "float64", "NULLABLE"),
    ("freeze", "INTEGER", "Int64", "NULLABLE"),
    ("lineid", "STRING", "string", "NULLABLE"),
    ("number", "STRING", "string", "NULLABLE"),
    ("remark", "STRING", "string", "NULLABLE"),
    ("status", "STRING", "string", "NULLABLE"),
    ("vattype", "INTEGER", "Int64", "NULLABLE"),
    ("version", "INTEGER", "Int64", "NULLABLE"),
    ("discount", "STRING", "string", "NULLABLE"),
    ("createdby", "INTEGER", "Int64", "NULLABLE"),
    ("ordertype", "INTEGER", "Int64", "NULLABLE"),
    ("reference", "STRING", "string", "NULLABLE"),
    ("sharelink", "INTEGER", "Int64", "NULLABLE"),
    ("vatamount", "FLOAT", "float64", "NULLABLE"),
    ("customerid", "STRING", "string", "NULLABLE"),
    ("facebookid", "STRING", "string", "NULLABLE"),
    ("trackingno", "STRING", "string", "NULLABLE"),
    ("vatpercent", "FLOAT", "float64", "NULLABLE"),
    ("description", "STRING", "string", "NULLABLE"),
    ("shippingvat", "INTEGER", "Int64", "NULLABLE"),
    ("createuserid", "INTEGER", "Int64", "NULLABLE"),
    ("customercode", "STRING", "string", "NULLABLE"),
    ("customername", "STRING", "string", "NULLABLE"),
    ("facebookname", "INTEGER", "Int64", "NULLABLE"),
    ("saleschannel", "STRING", "string", "NULLABLE"),
    ("shippingname", "STRING", "string", "NULLABLE"),
    ("amount_pretax", "INTEGER", "Int64", "NULLABLE"),
    ("customeremail", "STRING", "string", "NULLABLE"),
    ("customerphone", "STRING", "string", "NULLABLE"),
    ("paymentamount", "FLOAT", "float64", "NULLABLE"),
    ("paymentmethod", "STRING", "string", "NULLABLE"),
    ("paymentstatus", "STRING", "string", "NULLABLE"),
    ("remark_status", "STRING", "string", "NULLABLE"),
    ("return_status", "STRING", "string", "NULLABLE"),
    ("shippingemail", "STRING", "string", "NULLABLE"),
    ("shippingphone", "STRING", "string", "NULLABLE"),
    ("voucheramount", "FLOAT", "float64", "NULLABLE"),
    ("warehousecode", "STRING", "string", "NULLABLE"),
    ("createusername", "STRING", "string", "NULLABLE"),
    ("discountamount", "FLOAT", "float64", "NULLABLE"),
    ("sellerdiscount", "FLOAT", "float64", "NULLABLE"),
    ("shippingamount", "FLOAT", "float64", "NULLABLE"),
    ("customeraddress", "STRING", "string", "NULLABLE"),
    ("discount_amount", "FLOAT", "float64", "NULLABLE"),
    ("integrationName", "STRING", "string", "NULLABLE"),
    ("integrationShop", "STRING", "string", "NULLABLE"),
    ("marketplacename", "INTEGER", "Int64", "NULLABLE"),
    ("orderdateString", "STRING", "string", "NULLABLE"),
    ("platform_status", "STRING", "string", "NULLABLE"),
    ("seller_discount", "FLOAT", "float64", "NULLABLE"),
    ("shipping_amount", "FLOAT", "float64", "NULLABLE"),
    ("shippingaddress", "STRING", "string", "NULLABLE"),
    ("shippingchannel", "STRING", "string", "NULLABLE"),
    ("customerbranchno", "STRING", "string", "NULLABLE"),
    ("customeridnumber", "STRING", "string", "NULLABLE"),
    ("customerpostcode", "STRING", "string", "NULLABLE"),
    ("expireDateString", "INTEGER", "Int64", "NULLABLE"),
    ("platformdiscount", "FLOAT", "float64", "NULLABLE"),
    ("shippingdistrict", "STRING", "string", "NULLABLE"),
    ("shippingpostcode", "STRING", "string", "NULLABLE"),
    ("shippingprovince", "STRING", "string", "NULLABLE"),
    ("platform_discount", "FLOAT", "float64", "NULLABLE"),
    ("customerbranchname", "STRING", "string", "NULLABLE"),
    ("marketplacepayment", "INTEGER", "Int64", "NULLABLE"),
    ("shippingdateString", "INTEGER", "Int64", "NULLABLE"),
    ("is_confirm_received", "BOOLEAN", "boolean", "NULLABLE"),
    ("shipping_sorting_no", "STRING", "string", "NULLABLE"),
    ("shippingsubdistrict", "STRING", "string", "NULLABLE"),
    ("createdatetimeString", "STRING", "string", "NULLABLE"),
    ("updatedatetimeString", "STRING", "string", "NULLABLE"),
    ("orderManagementSystem", "STRING", "string", "NULLABLE"),
    ("paymentdatetimeString", "INTEGER", "Int64", "NULLABLE"),
    ("original_shipping_amount", "FLOAT", "float64", "NULLABLE"),
    ("seller_shipping_discount", "FLOAT", "float64", "NULLABLE"),
    ("marketplaceshippingstatus", "INTEGER", "Int64", "NULLABLE"),
    ("platform_shipping_discount", "FLOAT", "float64", "NULLABLE"),
    ("source_id", "INTEGER", "Int64", "NULLABLE"),
]


# Helper functions to generate schemas from unified definitions
def create_bigquery_schema(field_definitions):
    """Generate BigQuery schema from field definitions."""
    return [
        bigquery.SchemaField(name, bq_type, mode=mode)
        for name, bq_type, _, mode in field_definitions
    ]


def create_pandas_schema_mapping(field_definitions):
    """Generate pandas data type mapping from field definitions."""
    return {name: pandas_type for name, _, pandas_type, _ in field_definitions}


# Generated schemas and mappings
ORDER_ITEMS_SCHEMA = create_bigquery_schema(ORDER_ITEMS_FIELD_DEFINITIONS)
ORDERS_SCHEMA = create_bigquery_schema(ORDERS_FIELD_DEFINITIONS)
ORDER_ITEMS_SCHEMA_MAPPING = create_pandas_schema_mapping(ORDER_ITEMS_FIELD_DEFINITIONS)
ORDERS_SCHEMA_MAPPING = create_pandas_schema_mapping(ORDERS_FIELD_DEFINITIONS)


# Schema mappings for data preparation
ORDER_ITEMS_SCHEMA_MAPPING = {
    "id": "Int64",
    "sku": "string",
    "name": "string",
    "number": "float64",
    "status": "string",
    "skutype": "Int64",
    "discount": "string",
    "mfg_date": "Int64",
    "unittext": "string",
    "productid": "Int64",
    "totalprice": "float64",
    "producttype": "Int64",
    "sku_barcode": "string",
    "serialnolist": "object",  # Will handle REPEATED field separately
    "discountamount": "float64",
    "eso_vatpercent": "float64",
    "original_price": "float64",
    "pricepernumber": "float64",
    "return_quantity": "float64",
    "seller_discount": "float64",
    "shipping_amount": "float64",
    "platform_discount": "float64",
    "original_shipping_amount": "float64",
    "seller_shipping_discount": "float64",
    "platform_shipping_discount": "float64",
    "order_id": "Int64",
}

ORDERS_SCHEMA_MAPPING = {
    "id": "Int64",
    "tag": "object",  # Will handle REPEATED field separately
    "line": "Int64",
    "isCOD": "boolean",
    "amount": "float64",
    "freeze": "Int64",
    "lineid": "string",
    "number": "string",
    "remark": "string",
    "status": "string",
    "vattype": "Int64",
    "version": "Int64",
    "discount": "string",
    "createdby": "Int64",
    "ordertype": "Int64",
    "reference": "string",
    "sharelink": "Int64",
    "vatamount": "float64",
    "customerid": "string",
    "facebookid": "string",
    "trackingno": "string",
    "vatpercent": "float64",
    "description": "string",
    "shippingvat": "Int64",
    "createuserid": "Int64",
    "customercode": "string",
    "customername": "string",
    "facebookname": "Int64",
    "saleschannel": "string",
    "shippingname": "string",
    "amount_pretax": "Int64",
    "customeremail": "string",
    "customerphone": "string",
    "paymentamount": "float64",
    "paymentmethod": "string",
    "paymentstatus": "string",
    "remark_status": "string",
    "return_status": "string",
    "shippingemail": "string",
    "shippingphone": "string",
    "voucheramount": "float64",
    "warehousecode": "string",
    "createusername": "string",
    "discountamount": "float64",
    "sellerdiscount": "float64",
    "shippingamount": "float64",
    "customeraddress": "string",
    "discount_amount": "float64",
    "integrationName": "string",
    "integrationShop": "string",
    "marketplacename": "Int64",
    "orderdateString": "string",
    "platform_status": "string",
    "seller_discount": "float64",
    "shipping_amount": "float64",
    "shippingaddress": "string",
    "shippingchannel": "string",
    "customerbranchno": "string",
    "customeridnumber": "string",
    "customerpostcode": "string",
    "expireDateString": "Int64",
    "platformdiscount": "float64",
    "shippingdistrict": "string",
    "shippingpostcode": "string",
    "shippingprovince": "string",
    "platform_discount": "float64",
    "customerbranchname": "string",
    "marketplacepayment": "Int64",
    "shippingdateString": "Int64",
    "is_confirm_received": "boolean",
    "shipping_sorting_no": "string",
    "shippingsubdistrict": "string",
    "createdatetimeString": "string",
    "updatedatetimeString": "string",
    "orderManagementSystem": "string",
    "paymentdatetimeString": "Int64",
    "original_shipping_amount": "float64",
    "seller_shipping_discount": "float64",
    "marketplaceshippingstatus": "Int64",
    "platform_shipping_discount": "float64",
    "source_id": "Int64",
}


def convert_repeated_field_to_int_list(val):
    """
    Convert a value to a list of integers for BigQuery REPEATED fields.
    Handles various input formats including JSON strings and existing lists.
    """
    if pd.isna(val) or val is None:
        return []
    if isinstance(val, list):
        return [int(x) for x in val if pd.notna(x)]
    if isinstance(val, str):
        try:
            # Try to parse as JSON array
            import json

            parsed = json.loads(val)
            if isinstance(parsed, list):
                return [int(x) for x in parsed if pd.notna(x)]
        except (json.JSONDecodeError, ValueError, TypeError):
            pass
    return []


def prepare_dataframe_for_bigquery(
    df, schema_mapping, repeated_fields=None, special_defaults=None
):
    """
    Generic function to prepare DataFrame to match BigQuery schema.
    Ensures data types and handles missing columns.

    Args:
        df: Input DataFrame
        schema_mapping: Dictionary mapping column names to pandas data types
        repeated_fields: List of column names that are REPEATED fields in BigQuery
        special_defaults: Dictionary of column names to default values
    """
    # Create a copy to avoid modifying the original
    df_prepared = df.copy()

    if repeated_fields is None:
        repeated_fields = []
    if special_defaults is None:
        special_defaults = {}

    # Add missing columns with None values
    for col in schema_mapping.keys():
        if col not in df_prepared.columns:
            df_prepared[col] = None

    # Convert data types
    for col, dtype in schema_mapping.items():
        if col in df_prepared.columns:
            try:
                if dtype == "string":
                    df_prepared[col] = df_prepared[col].astype("string")
                elif dtype == "Int64":
                    df_prepared[col] = pd.to_numeric(
                        df_prepared[col], errors="coerce"
                    ).astype("Int64")
                elif dtype == "float64":
                    df_prepared[col] = pd.to_numeric(
                        df_prepared[col], errors="coerce"
                    ).astype("float64")
                elif dtype == "boolean":
                    df_prepared[col] = df_prepared[col].astype("boolean")
                elif col in repeated_fields:
                    # Handle REPEATED fields
                    df_prepared[col] = df_prepared[col].apply(
                        convert_repeated_field_to_int_list
                    )
            except Exception as e:
                print(f"Warning: Could not convert column {col} to {dtype}: {e}")

    # Apply special defaults
    for col, default_value in special_defaults.items():
        if col in df_prepared.columns:
            df_prepared[col] = df_prepared[col].fillna(default_value)

    # Ensure we only have the columns defined in the schema
    df_prepared = df_prepared[list(schema_mapping.keys())]

    return df_prepared


def prepare_order_items_data(df_items):
    """
    Prepare order items DataFrame to match BigQuery schema.
    """
    return prepare_dataframe_for_bigquery(
        df_items,
        ORDER_ITEMS_SCHEMA_MAPPING,
        repeated_fields=["serialnolist"],
        special_defaults={"return_quantity": 0.0},
    )


def prepare_orders_data(df_orders):
    """
    Prepare orders DataFrame to match BigQuery schema.
    """
    return prepare_dataframe_for_bigquery(
        df_orders, ORDERS_SCHEMA_MAPPING, repeated_fields=["tag"]
    )


def generate_merge_sql(target_table, staging_table, schema_mapping, match_conditions):
    """
    Generate BigQuery MERGE SQL statement dynamically based on schema mapping.

    Args:
        target_table: Target table name
        staging_table: Staging table name
        schema_mapping: Dictionary mapping column names to data types
        match_conditions: List of column names to use for matching records
    """
    columns = list(schema_mapping.keys())

    # Generate column assignments for UPDATE
    update_assignments = [f"target.{col} = src.{col}" for col in columns]
    update_clause = ",\n            ".join(update_assignments)

    # Generate column lists for INSERT
    insert_columns = ", ".join(columns)
    insert_values = ", ".join([f"src.{col}" for col in columns])

    # Generate match conditions
    match_clause = " AND ".join(
        [f"target.{col} = src.{col}" for col in match_conditions]
    )

    merge_sql = f"""
        MERGE `{target_table}` AS target
        USING `{staging_table}` AS src
        ON {match_clause}
        WHEN MATCHED THEN
        UPDATE SET
            {update_clause}
        WHEN NOT MATCHED THEN
        INSERT (
            {insert_columns}
        ) VALUES (
            {insert_values}
        )
        """

    return merge_sql


def load_dataframe_to_bigquery(
    client,
    df,
    table_name,
    schema,
    write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
):
    """
    Load DataFrame to BigQuery table with specified schema.

    Args:
        client: BigQuery client
        df: DataFrame to load
        table_name: Target table name
        schema: BigQuery schema
        write_disposition: Write disposition (default: WRITE_TRUNCATE)
    """
    job_config = bigquery.LoadJobConfig(
        write_disposition=write_disposition,
        schema=schema,
    )
    load_job = client.load_table_from_dataframe(df, table_name, job_config=job_config)
    load_job.result()
    return load_job


class BigQueryOrderImportAPI(APIView):
    authentication_classes = []
    permission_classes = []

    class Validator(serializers.Serializer):
        start_date = serializers.DateTimeField()
        end_date = serializers.DateTimeField()
        company = serializers.IntegerField(allow_null=True, required=False)

    def post(self, request):
        """
        Handle POST requests to import orders into BigQuery.
        """
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        start_date = data["start_date"]
        end_date = data["end_date"]
        company = data.get("company")

        # BigQuery setup
        BQ_DATASET = "dobybot.report_api_tester"
        STG_ORDERS_TABLE = f"{BQ_DATASET}.staging_orders"
        STG_ITEMS_TABLE = f"{BQ_DATASET}.staging_order_items"
        ORDERS_TABLE = f"{BQ_DATASET}.orders"
        ITEMS_TABLE = f"{BQ_DATASET}.order_items"
        bq_client = bigquery.Client()

        # 1. fetch raw JSON rows
        pick_orders = PickOrder.objects.filter(
            update_date__gte=start_date,
            update_date__lte=end_date,
        )

        if company:
            pick_orders = pick_orders.filter(company=company)

        orders_list = []
        items_list = []

        # 2. deserialize and flatten
        for rec in pick_orders.values("id", "order_json"):
            od = rec["order_json"]
            # extract top-level order fields
            order_record = {
                k: od[k] for k in od if k not in ("list", "payments", "extra")
            }
            order_record["source_id"] = rec["id"]
            orders_list.append(order_record)

            # extract line items
            for item in od["list"]:
                item_flat = item.copy()
                item_flat["order_id"] = od["id"]
                items_list.append(item_flat)

        df_orders = pd.DataFrame(orders_list)
        df_items = pd.DataFrame(items_list)

        # 2. Load into staging tables, truncating them:
        # Prepare and load orders data
        df_orders_prepared = prepare_orders_data(df_orders)
        load_dataframe_to_bigquery(
            bq_client, df_orders_prepared, STG_ORDERS_TABLE, ORDERS_SCHEMA
        )

        # Prepare and load items data
        df_items_prepared = prepare_order_items_data(df_items)
        load_dataframe_to_bigquery(
            bq_client, df_items_prepared, STG_ITEMS_TABLE, ORDER_ITEMS_SCHEMA
        )

        # 3. MERGE staging → production (dedupe on order_id/[item_id]):
        merge_orders_sql = generate_merge_sql(
            ORDERS_TABLE, STG_ORDERS_TABLE, ORDERS_SCHEMA_MAPPING, ["number"]
        )

        merge_items_sql = generate_merge_sql(
            ITEMS_TABLE,
            STG_ITEMS_TABLE,
            ORDER_ITEMS_SCHEMA_MAPPING,
            ["order_id", "sku"],
        )

        bq_client.query(merge_orders_sql).result()
        bq_client.query(merge_items_sql).result()

        return Response(
            {
                "status": "success",
                "message": f"Loaded {len(df_orders_prepared)} orders & {len(df_items_prepared)} items with schema enforcement.",
            },
            status=200,
        )
